<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Modal Click Issues</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            padding: 2rem;
            background: #f8f9fa;
        }
        
        .debug-info {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            border-left: 4px solid #007bff;
        }
        
        .click-test {
            background: #e3f2fd;
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .click-test:hover {
            background: #bbdefb;
        }
        
        .click-test.clicked {
            background: #4caf50;
            color: white;
        }
        
        #log {
            background: #000;
            color: #0f0;
            padding: 1rem;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Modal Click Debug Tool</h1>
        
        <div class="debug-info">
            <h5>Instructions:</h5>
            <ol>
                <li>Click the test elements below - they should respond</li>
                <li>Open the modal</li>
                <li>Try clicking the test elements again - they should NOT respond when modal is open</li>
                <li>Close the modal</li>
                <li>Try clicking the test elements again - they SHOULD respond after modal closes</li>
            </ol>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <h5>Click Test Elements</h5>
                <div class="click-test" data-test="1">Click Test 1 - Should work when modal is closed</div>
                <div class="click-test" data-test="2">Click Test 2 - Should work when modal is closed</div>
                <div class="click-test" data-test="3">Click Test 3 - Should work when modal is closed</div>
                
                <button class="btn btn-primary mt-3" id="openModalBtn">Open Test Modal</button>
                <button class="btn btn-secondary mt-3" id="clearLogBtn">Clear Log</button>
            </div>
            
            <div class="col-md-6">
                <h5>Debug Log</h5>
                <div id="log"></div>
            </div>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="testModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Test Modal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                    <div class="alert alert-info">
                        <h6>Modal is Open</h6>
                        <p>While this modal is open, the click test elements behind should NOT respond to clicks.</p>
                        <p>After closing this modal, they should work again.</p>
                    </div>
                    
                    <div style="height: 400px; background: linear-gradient(to bottom, #e3f2fd, #bbdefb); padding: 1rem; border-radius: 4px;">
                        <p>This is scrollable content inside the modal.</p>
                        <p>You should be able to scroll here.</p>
                        <div style="margin-top: 100px;">
                            <p>Middle content...</p>
                        </div>
                        <div style="margin-top: 100px;">
                            <p>Bottom content...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="testActionBtn">Test Action</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const log = document.getElementById('log');
        
        function addLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            addLog('Page loaded - click debugging started');
            
            // Test click elements
            document.querySelectorAll('.click-test').forEach(element => {
                element.addEventListener('click', function(e) {
                    const testNum = this.dataset.test;
                    addLog(`✅ Click Test ${testNum} - CLICKED (this should work when modal is closed)`);
                    this.classList.add('clicked');
                    setTimeout(() => this.classList.remove('clicked'), 1000);
                });
            });
            
            // Modal elements
            const testModal = document.getElementById('testModal');
            const openModalBtn = document.getElementById('openModalBtn');
            const clearLogBtn = document.getElementById('clearLogBtn');
            
            // Open modal button
            openModalBtn.addEventListener('click', function() {
                addLog('🔵 Opening modal...');
                const modal = new bootstrap.Modal(testModal, {
                    backdrop: 'static',
                    keyboard: true,
                    focus: true
                });
                
                // Modal event listeners
                testModal.addEventListener('show.bs.modal', function() {
                    addLog('🔵 Modal show event - backdrop should block clicks');
                });
                
                testModal.addEventListener('shown.bs.modal', function() {
                    addLog('🔵 Modal shown event - modal is fully open');
                });
                
                testModal.addEventListener('hide.bs.modal', function() {
                    addLog('🔴 Modal hide event - starting to close');
                });
                
                testModal.addEventListener('hidden.bs.modal', function() {
                    addLog('🔴 Modal hidden event - fully closed, clicks should work again');
                    
                    // Clean up
                    document.body.classList.remove('modal-open');
                    document.body.style.removeProperty('overflow');
                    document.body.style.removeProperty('padding-right');
                    
                    const backdrops = document.querySelectorAll('.modal-backdrop');
                    backdrops.forEach(backdrop => {
                        addLog(`🧹 Removing stuck backdrop: ${backdrop.className}`);
                        backdrop.remove();
                    });
                });
                
                modal.show();
            });
            
            // Clear log button
            clearLogBtn.addEventListener('click', function() {
                log.innerHTML = '';
                addLog('Log cleared');
            });
            
            // Test action button in modal
            document.getElementById('testActionBtn').addEventListener('click', function() {
                addLog('✅ Modal action button clicked - this should always work');
            });
            
            // Global click listener to detect any clicks
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.click-test') && !e.target.closest('button') && !e.target.closest('.modal')) {
                    addLog(`🖱️ Global click detected on: ${e.target.tagName.toLowerCase()}`);
                }
            });
            
            // Check for stuck elements periodically
            setInterval(function() {
                const backdrops = document.querySelectorAll('.modal-backdrop');
                const modalOpen = document.body.classList.contains('modal-open');
                const modalVisible = document.querySelector('.modal.show');
                
                if (backdrops.length > 0 && !modalVisible) {
                    addLog(`⚠️ WARNING: Found ${backdrops.length} stuck backdrop(s) with no visible modal`);
                }
                
                if (modalOpen && !modalVisible) {
                    addLog(`⚠️ WARNING: Body has modal-open class but no modal is visible`);
                }
            }, 2000);
        });
    </script>
</body>
</html>
