"""
Test suite for the translator bot preview functionality.
"""
import unittest
import tempfile
import os
import json
from unittest.mock import Mock, patch, MagicMock
import pandas as pd
from openpyxl import Workbook

# Import the classes we want to test
from src.backend.blueprints.translator_bot.translation_service import TranslationService
from src.backend.blueprints.translator_bot.excel_handler import ExcelHandler
from src.backend.blueprints.translator_bot.word_handler import WordHandler
from src.backend.blueprints.translator_bot.pptx_handler import PPTXHandler


class TestTranslatorPreview(unittest.TestCase):
    """Test cases for translator preview functionality."""

    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.user_id = "test_user_123"
        
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def create_test_excel_file(self, filename="test.xlsx"):
        """Create a test Excel file for testing."""
        file_path = os.path.join(self.temp_dir, filename)
        
        # Create test data
        data = {
            'Name': ['<PERSON>', '<PERSON>', '<PERSON>'],
            'Description': ['Software Engineer', 'Product Manager', 'Data Scientist'],
            'Location': ['New York', 'San Francisco', 'Seattle']
        }
        df = pd.DataFrame(data)
        df.to_excel(file_path, index=False)
        
        return file_path

    def create_test_word_file(self, filename="test.docx"):
        """Create a test Word file for testing."""
        file_path = os.path.join(self.temp_dir, filename)
        
        from docx import Document
        doc = Document()
        doc.add_paragraph("This is a test paragraph.")
        doc.add_paragraph("This is another test paragraph with more content.")
        doc.add_paragraph("Final test paragraph.")
        doc.save(file_path)
        
        return file_path

    def test_excel_handler_preview_data(self):
        """Test ExcelHandler get_preview_data method."""
        # Create test Excel file
        excel_path = self.create_test_excel_file()
        
        # Create handler
        handler = ExcelHandler(excel_path)
        
        # Get preview data
        preview_data = handler.get_preview_data(max_items=5)
        
        # Assertions
        self.assertIsInstance(preview_data, dict)
        self.assertIn('file_info', preview_data)
        self.assertIn('samples', preview_data)
        self.assertIn('statistics', preview_data)
        
        # Check file info
        file_info = preview_data['file_info']
        self.assertEqual(file_info['total_rows'], 3)
        self.assertEqual(file_info['total_columns'], 3)
        self.assertIn('Name', file_info['selected_columns'])
        
        # Check samples
        samples = preview_data['samples']
        self.assertGreater(len(samples), 0)
        self.assertLessEqual(len(samples), 5)  # Should respect max_items
        
        # Check sample structure
        for sample in samples:
            self.assertIn('column', sample)
            self.assertIn('content', sample)

    def test_word_handler_preview_data(self):
        """Test WordHandler get_preview_data method."""
        # Create test Word file
        word_path = self.create_test_word_file()
        
        # Create handler
        handler = WordHandler(word_path, file_context="Test context")
        
        # Get preview data
        preview_data = handler.get_preview_data(max_items=5)
        
        # Assertions
        self.assertIsInstance(preview_data, dict)
        self.assertIn('file_info', preview_data)
        self.assertIn('samples', preview_data)
        self.assertIn('statistics', preview_data)
        
        # Check file info
        file_info = preview_data['file_info']
        self.assertGreater(file_info['total_paragraphs'], 0)
        self.assertGreater(file_info['non_empty_paragraphs'], 0)
        
        # Check samples
        samples = preview_data['samples']
        self.assertGreater(len(samples), 0)
        self.assertLessEqual(len(samples), 5)  # Should respect max_items
        
        # Check sample structure
        for sample in samples:
            self.assertIn('text', sample)
            self.assertIn('type', sample)
            self.assertEqual(sample['type'], 'paragraph')

    @patch('src.backend.blueprints.translator_bot.translation_service.current_app')
    def test_translation_service_preview_excel(self, mock_app):
        """Test TranslationService get_preview method for Excel files."""
        # Mock logger
        mock_app.logger = Mock()
        
        # Create test Excel file
        excel_path = self.create_test_excel_file()
        
        # Create translation service
        with patch.object(TranslationService, '__init__', lambda x, y, z=None: None):
            service = TranslationService.__new__(TranslationService)
            service.user_id = self.user_id
            service.original_filename = "test.xlsx"
            service.file_path = excel_path
            service.translator = Mock()
            
            # Mock get_file_handler to return 'excel'
            service.get_file_handler = Mock(return_value='excel')
            
            # Get preview
            result = service.get_preview(
                target_language="es",
                selected_columns=["Name", "Description"],
                max_items=3
            )
            
            # Assertions
            self.assertTrue(result['success'])
            self.assertEqual(result['file_type'], 'excel')
            self.assertIn('preview_data', result)
            
            preview_data = result['preview_data']
            self.assertIn('file_info', preview_data)
            self.assertIn('samples', preview_data)

    def test_excel_handler_preview_with_selected_columns(self):
        """Test ExcelHandler preview with specific columns selected."""
        # Create test Excel file
        excel_path = self.create_test_excel_file()
        
        # Create handler
        handler = ExcelHandler(excel_path)
        
        # Get preview data with selected columns
        selected_columns = ["Name", "Description"]
        preview_data = handler.get_preview_data(selected_columns=selected_columns, max_items=5)
        
        # Assertions
        self.assertIsInstance(preview_data, dict)
        file_info = preview_data['file_info']
        self.assertEqual(file_info['selected_columns'], selected_columns)
        
        # Check that samples only contain selected columns
        samples = preview_data['samples']
        for sample in samples:
            self.assertIn(sample['column'], selected_columns)

    def test_preview_data_structure_consistency(self):
        """Test that all handlers return consistent preview data structure."""
        # Test Excel handler
        excel_path = self.create_test_excel_file()
        excel_handler = ExcelHandler(excel_path)
        excel_preview = excel_handler.get_preview_data(max_items=3)
        
        # Test Word handler
        word_path = self.create_test_word_file()
        word_handler = WordHandler(word_path, file_context="Test")
        word_preview = word_handler.get_preview_data(max_items=3)
        
        # Check consistent structure
        for preview in [excel_preview, word_preview]:
            self.assertIn('file_info', preview)
            self.assertIn('samples', preview)
            self.assertIn('statistics', preview)
            self.assertIsInstance(preview['file_info'], dict)
            self.assertIsInstance(preview['samples'], list)
            self.assertIsInstance(preview['statistics'], dict)

    def test_preview_max_items_limit(self):
        """Test that preview respects max_items limit."""
        # Create test Excel file with more data
        file_path = os.path.join(self.temp_dir, "large_test.xlsx")
        
        # Create larger dataset
        data = {
            'Column1': [f'Value {i}' for i in range(20)],
            'Column2': [f'Description {i}' for i in range(20)],
            'Column3': [f'Location {i}' for i in range(20)]
        }
        df = pd.DataFrame(data)
        df.to_excel(file_path, index=False)
        
        # Create handler
        handler = ExcelHandler(file_path)
        
        # Test with different max_items values
        for max_items in [1, 3, 5]:
            preview_data = handler.get_preview_data(max_items=max_items)
            samples = preview_data['samples']
            self.assertLessEqual(len(samples), max_items)


if __name__ == '__main__':
    unittest.main()
