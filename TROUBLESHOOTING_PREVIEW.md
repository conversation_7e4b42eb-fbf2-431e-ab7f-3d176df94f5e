# Troubleshooting Translation Preview Feature

## Issue: 404 Error when accessing /translator/api/preview

### Symptoms
- <PERSON>rowser console shows: `404 NOT FOUND` when clicking "Preview Translation"
- Error message: `SyntaxError: JSON.parse: unexpected character at line 1 column 1`

### Root Cause
The Flask application needs to be restarted to pick up the new preview route that was added.

### Solution Steps

#### 1. Restart Flask Application
The most common cause is that the Flask server is still running the old code without the new preview route.

**Stop the current Flask server:**
- Press `Ctrl+C` in the terminal where <PERSON>lask is running
- Or kill the process if running in background

**Restart the Flask server:**
```bash
# Navigate to project directory
cd "c:\Users\<USER>\AI team projects\compli-bot"

# Activate virtual environment (if using one)
# venv\Scripts\activate

# Start Flask application
python -m src.backend.main
# OR
flask run
# OR however you normally start the application
```

#### 2. Verify Route Registration
Run the test script to verify the route is properly registered:

```bash
python test_preview_route.py
```

Expected output:
```
✅ SUCCESS: Translation preview route is properly registered!
   Route: /translator/api/preview
   Methods: {'POST', 'OPTIONS'}
   Endpoint: translator_bot_routes.get_translation_preview
```

#### 3. Check Application Logs
Look for any errors in the Flask application logs when starting up:

```bash
# Check for import errors or route registration issues
# Look for lines containing "preview" or "translator_bot"
```

#### 4. Verify Blueprint Registration
Ensure the translator_bot blueprint is properly registered in the main application:

In `src/__init__.py`, verify this line exists:
```python
from .backend.blueprints.translator_bot import translator_bot as translator_bot_blueprint
app.register_blueprint(translator_bot_blueprint, url_prefix='/translator')
```

#### 5. Test with curl (Optional)
Test the endpoint directly with curl to isolate frontend issues:

```bash
curl -X POST http://localhost:5000/translator/api/preview \
  -H "Content-Type: application/json" \
  -d '{"target_language": "es", "file_type": ".xlsx", "selected_columns": [], "max_items": 5}'
```

Expected response (if authenticated):
```json
{"error": "User not authenticated"}
```

### Alternative Debugging Steps

#### Check URL Mapping
If the route still doesn't work, add this debug code temporarily to see all registered routes:

```python
# Add to src/__init__.py after app creation
with app.app_context():
    for rule in app.url_map.iter_rules():
        if 'preview' in rule.rule:
            print(f"Preview route found: {rule.rule} -> {rule.endpoint}")
```

#### Verify File Changes
Ensure all files were saved correctly:
- `src/backend/blueprints/translator_bot/translation_routes.py` - Contains the new preview route
- `src/backend/blueprints/translator_bot/translation_service.py` - Contains get_preview method
- Template file - Contains updated JavaScript with correct URL

### Common Issues and Solutions

#### Issue: Import Errors
**Symptoms:** Application fails to start with import errors
**Solution:** Check that all new methods are properly implemented in the file handlers

#### Issue: Authentication Errors
**Symptoms:** 401 Unauthorized responses
**Solution:** Ensure you're logged in to the application before testing preview

#### Issue: File Not Found Errors
**Symptoms:** Preview fails with file not found
**Solution:** Upload a file first before trying to preview

#### Issue: AI Services Not Ready
**Symptoms:** Preview returns "AI services are still sipping their morning coffee"
**Solution:** Wait for AI services to initialize, or check AI service configuration

### Verification Checklist

- [ ] Flask application restarted
- [ ] No import errors in console
- [ ] Route appears in URL mapping
- [ ] User is authenticated
- [ ] File has been uploaded
- [ ] Target language is selected
- [ ] Browser cache cleared (if needed)

### Still Having Issues?

If the problem persists:

1. **Check the browser's Network tab** to see the exact request being made
2. **Verify the request URL** matches `/translator/api/preview`
3. **Check Flask logs** for any error messages
4. **Try a different browser** to rule out caching issues
5. **Verify all code changes were saved** and the server restarted

### Success Indicators

When working correctly, you should see:
- Preview button becomes enabled after file upload and language selection
- Clicking preview shows loading spinner
- Modal opens with preview content
- No 404 errors in browser console
- Flask logs show preview requests being processed
