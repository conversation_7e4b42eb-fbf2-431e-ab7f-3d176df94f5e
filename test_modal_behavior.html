<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Behavior Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            height: 200vh; /* Make page scrollable */
            background: linear-gradient(45deg, #f0f0f0, #e0e0e0);
            padding: 2rem;
        }
        
        .test-content {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* Modal styles from translator_tool.css */
        .modal.show {
            padding-right: 0 !important;
        }
        
        #previewModal .modal-dialog {
            max-width: 90vw;
            margin: 1rem auto;
        }
        
        #previewModal .modal-content {
            max-height: 90vh;
            display: flex;
            flex-direction: column;
        }
        
        #previewModal .modal-body {
            flex: 1;
            overflow-y: auto;
            max-height: calc(90vh - 120px);
            padding: 1.5rem;
        }
        
        .preview-summary {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="test-content">
        <h1>Modal Behavior Test</h1>
        <p>This page tests the modal behavior to ensure it doesn't interfere with page scrolling.</p>
        <p>Scroll down to see more content, then click the button to open the modal.</p>
        <button class="btn btn-primary" id="testModalBtn">
            <i class="fas fa-eye me-2"></i>
            Test Preview Modal
        </button>
    </div>
    
    <div class="test-content">
        <h2>More Content</h2>
        <p>This is additional content to make the page scrollable.</p>
        <p>When the modal is open, the page should not scroll.</p>
        <p>When the modal is closed, page scrolling should be restored.</p>
    </div>
    
    <div class="test-content">
        <h2>Even More Content</h2>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>
        <p>Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco.</p>
    </div>

    <!-- Preview Modal -->
    <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewModalLabel">
                        <i class="fas fa-eye me-2"></i>
                        Translation Preview Test
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                    <div id="previewContent">
                        <div class="preview-summary">
                            <h6><i class="fas fa-info-circle me-2"></i>Test Preview Summary</h6>
                            <p>This is a test modal to verify scrolling behavior.</p>
                            <p>The page behind should not scroll when this modal is open.</p>
                        </div>
                        
                        <div class="alert alert-info">
                            <h6>Test Instructions:</h6>
                            <ul>
                                <li>Try scrolling with mouse wheel - page should not move</li>
                                <li>Try scrolling inside this modal - should work normally</li>
                                <li>Press Escape key - modal should close</li>
                                <li>Click outside modal - modal should close</li>
                                <li>After closing, page scrolling should work again</li>
                            </ul>
                        </div>
                        
                        <div style="height: 800px; background: linear-gradient(to bottom, #e3f2fd, #bbdefb); padding: 2rem; border-radius: 8px;">
                            <h6>Scrollable Content Inside Modal</h6>
                            <p>This content is tall enough to make the modal body scrollable.</p>
                            <p>You should be able to scroll within this modal.</p>
                            <p>But the page behind should remain fixed.</p>
                            
                            <div style="margin-top: 200px;">
                                <h6>Middle Section</h6>
                                <p>More content to test scrolling...</p>
                            </div>
                            
                            <div style="margin-top: 200px;">
                                <h6>Bottom Section</h6>
                                <p>This is the bottom of the modal content.</p>
                                <p>If you can see this, modal scrolling is working!</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="testProceedBtn">
                        <i class="fas fa-check me-2"></i>
                        Test Proceed Button
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const testModalBtn = document.getElementById('testModalBtn');
            const previewModalElement = document.getElementById('previewModal');
            
            testModalBtn.addEventListener('click', function() {
                const previewModal = new bootstrap.Modal(previewModalElement, {
                    backdrop: true,
                    keyboard: true,
                    focus: true
                });
                
                // Handle modal events to prevent page scrolling issues
                previewModalElement.addEventListener('show.bs.modal', function () {
                    document.body.style.overflow = 'hidden';
                    console.log('Modal opening - body scroll disabled');
                });
                
                previewModalElement.addEventListener('shown.bs.modal', function () {
                    const modalBody = previewModalElement.querySelector('.modal-body');
                    if (modalBody) {
                        modalBody.scrollTop = 0;
                    }
                    console.log('Modal opened - scroll reset to top');
                });
                
                previewModalElement.addEventListener('hidden.bs.modal', function () {
                    document.body.style.overflow = '';
                    console.log('Modal closed - body scroll restored');
                });
                
                previewModal.show();
                
                // Handle proceed button
                document.getElementById('testProceedBtn').onclick = function() {
                    alert('Proceed button clicked!');
                    previewModal.hide();
                };
            });
        });
    </script>
</body>
</html>
