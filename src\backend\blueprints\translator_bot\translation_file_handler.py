from abc import ABC, abstractmethod

class TranslationFileHandler(ABC):
    """
    Abstract base class for translation file handlers (Excel, Word, PowerPoint).
    All handlers must implement these methods for compatibility with TranslationService.
    """
    def __init__(self, file_path):
        self.file_path = file_path

    @abstractmethod
    def get_batches(self, selected_columns=None, max_rows=200):
        """
        Return iterable batches of content to translate.
        """
        pass

    @abstractmethod
    def write_results_to_file(self, batch_results, lang):
        """
        Write translated results to the output file for the given language.
        """
        pass

    @abstractmethod
    def get_total_batches(self, selected_columns, target_languages):
        """
        Return the total number of batches for progress tracking.
        """
        pass

    @abstractmethod
    def get_preview_data(self, selected_columns=None, max_items=10):
        """
        Return a preview of content that would be translated.

        Args:
            selected_columns (list): List of column names to preview (for Excel files)
            max_items (int): Maximum number of items to include in preview

        Returns:
            dict: Preview data containing sample content and metadata
        """
        pass
