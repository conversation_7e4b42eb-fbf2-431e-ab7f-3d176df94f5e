<!-- templates/translator_bot/translation_tool.html -->

{% extends "base.html" %}

{% block head_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="{{ url_for('static', filename='translator_tool.css') }}">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="translation-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10 col-xl-8">
                <div class="translation-card">
                    <div class="card-header-custom position-relative text-center" style="display: flex; flex-direction: column; align-items: center; padding-top: 0;">
                        <span class="header-icon-top" style="font-size: 5rem; margin-bottom: 0.1rem; margin-top: 0; display: block;">
                            <i class="fas fa-language"></i>
                        </span>
                        <h1 class="mb-0" style="font-size: 2.5rem; font-weight: 700;">Document Translation Tool</h1>
                        <p class="mb-0 mt-2 opacity-75">
                            Translate documents with AI
                        </p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- File Upload Section -->
                        <div class="mb-4 mt-4">                            
                            <div class="upload-area" id="uploadArea">
                                <div class="upload-icon">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                </div>
                                <h6>Drag & drop your file here</h6>
                                <p class="text-muted mb-3">or click to browse</p>
                                <p class="small text-muted">
                                    Supported formats: Excel (.xlsx), PowerPoint (.pptx), Word (.docx), PDF (.pdf)
                                    <br>Maximum file size: 50MB
                                </p>
                                <input type="file" id="fileInput" accept=".xlsx,.pptx,.docx,.pdf" style="display: none;">
                            </div>
                            
                            <div id="fileInfo" class="file-info" style="display: none;">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-file me-3 text-dark"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold" id="fileName"></div>
                                        <div class="small text-muted" id="fileDetails"></div>
                                    </div>
                                    <button class="remove-file-btn" id="removeFile" type="button">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Loading Spinner -->
                            <div id="loadingSpinner" class="loading-spinner">
                                <div class="spinner"></div>
                                <div class="loading-text" id="loadingText">Uploading and processing file...</div>
                            </div>
                        </div>

                        <!-- File Options (Excel columns, Word paragraphs, PPTX slides) -->
                        <div id="fileOptions" class="excel-options" style="display: none;">
                            <div class="excel-options-header">
                                <h5 id="fileOptionsHeader"></h5>
                                <button type="button" class="select-all-btn" id="toggleAllColumns" style="display:none;"><i class="fas fa-check-square me-1"></i>Deselect All</button>
                            </div>
                            <p class="text-muted mb-3" id="fileOptionsDescription"></p>
                            <div class="row" id="optionCheckboxes">
                                <!-- Checkboxes will be populated dynamically -->
                            </div>
                        </div>

                        <!-- File Context Input & Language Selection -->
                        <div id="languageSettings" class="excel-options mt-4" style="display: none;">
                            <div class="excel-options-header">
                                <h5>
                                    <i class="fas fa-globe me-2"></i>
                                    Language Settings
                                </h5>
                            </div>
                            <!-- Context Input -->
                            <div class="mb-3">
                                <label for="fileContext" class="form-label fw-semibold">File Context (optional)</label>
                                <textarea class="form-control" id="fileContext" rows="3" placeholder="Add any relevant context or instructions for the model to improve the translation accuracy."></textarea>
                            </div>
                            <div class="language-grid">
                                <div>
                                    <label for="sourceLanguage" class="form-label fw-semibold">Source Language</label>
                                    <select class="form-select form-select-custom" id="sourceLanguage">
                                        <option value="auto">Auto-detect</option>
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="it">Italian</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="zh">Chinese</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="targetLanguage" class="form-label fw-semibold">Target Language(s)</label>
                                    <select class="form-select form-select-custom" id="targetLanguage" multiple="multiple" style="width: 100%;">
                                        <option value="ar">Arabic</option>
                                        <option value="zh">Chinese</option>
                                        <option value="da">Danish</option>
                                        <option value="nl">Dutch</option>
                                        <option value="en">English</option>
                                        <option value="fi">Finnish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="hi">Hindi</option>
                                        <option value="it">Italian</option>
                                        <option value="ja">Japanese</option>
                                        <option value="ko">Korean</option>
                                        <option value="no">Norwegian</option>
                                        <option value="pl">Polish</option>
                                        <option value="pt">Portuguese</option>
                                        <option value="ru">Russian</option>
                                        <option value="es">Spanish</option>
                                        <option value="sv">Swedish</option>
                                        <option value="th">Thai</option>
                                        <option value="tu">Turkish</option>
                                        <option value="vi">Vietnamese</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Preview and Translation Buttons -->
                        <div class="text-center mt-4">
                            <div class="d-flex justify-content-center gap-3 flex-wrap">
                                <button class="btn btn-outline-primary btn-lg" id="previewBtn" disabled>
                                    <i class="fas fa-eye me-2"></i>
                                    Preview Translation
                                </button>
                                <button class="translate-link-custom btn-lg" id="translateBtn" disabled>
                                    <i class="fas fa-magic me-2"></i>
                                    Start Translation
                                </button>
                            </div>
                        </div>

                        <!-- Preview Modal -->
                        <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-xl modal-dialog-scrollable">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="previewModalLabel">
                                            <i class="fas fa-eye me-2"></i>
                                            Translation Preview
                                        </h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body" style="max-height: 70vh; overflow-y: auto;">
                                        <div id="previewContent">
                                            <!-- Preview content will be loaded here -->
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                        <button type="button" class="btn btn-primary" id="proceedWithTranslation">
                                            <i class="fas fa-magic me-2"></i>
                                            Proceed with Full Translation
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block tail_js %}
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2 for targetLanguage
    $('#targetLanguage').select2({
        placeholder: 'Select target language(s)',
        allowClear: true,
        width: 'resolve'
    });


    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileDetails = document.getElementById('fileDetails');
    const removeFile = document.getElementById('removeFile');
    const fileOptions = document.getElementById('fileOptions');
    const optionCheckboxes = document.getElementById('optionCheckboxes');
    const toggleAllColumns = document.getElementById('toggleAllColumns');
    const translateBtn = document.getElementById('translateBtn');
    const previewBtn = document.getElementById('previewBtn');
    const targetLanguage = document.getElementById('targetLanguage');

    const loadingSpinner = document.getElementById('loadingSpinner');
    const loadingText = document.getElementById('loadingText');
    const languageSettings = document.getElementById('languageSettings');
    
    let selectedFile = null;
    let fileType = null;
    let currentSessionId = null; // Track current translation session
    let currentPreviewModal = null; // Track current preview modal instance

    // Ensure the translate and preview buttons are always disabled on page load
    translateBtn.hidden = true;
    previewBtn.hidden = true;

    // Note: File cleanup now only happens on new file upload (server-side)
    // Removed automatic cleanup on page unload/visibility change to preserve user files

    // Helper function to show alerts
    function showAlert(message, type = 'info') {
        // Create a simple alert div
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Upload area click handler
    uploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // Drag and drop handlers
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // File input change handler
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // Remove file handler
    removeFile.addEventListener('click', () => {
        // Note: File cleanup now only happens on new file upload
        // Removed cleanup here to preserve user files unless they upload a new one
        console.log('removeFile clicked - UI reset only, no file cleanup');
        
        selectedFile = null;
        currentSessionId = null;
        translationCompleted = false;
        fileInfo.style.display = 'none';
        uploadArea.style.display = 'block';
        fileOptions.style.display = 'none';
        loadingSpinner.style.display = 'none';
        fileInput.value = '';
        languageSettings.style.display = 'none';
        const existingDownloadLinks = document.querySelectorAll('.download-link-custom, .new-translation-btn');
        existingDownloadLinks.forEach(link => link.remove());
        updateTranslateButton();
    });

    // Target language change handler (Select2 event)
    $('#targetLanguage').on('change', function() {
        updateTranslateButton();
        showTranslateButtonAfterChange();
    });

    // Update button state when columns are selected/deselected
    optionCheckboxes.addEventListener('change', function() {
        updateTranslateButton();
        showTranslateButtonAfterChange();
    });

    // Context change handler
    const fileContextTextarea = document.getElementById('fileContext');
    fileContextTextarea.addEventListener('input', showTranslateButtonAfterChange);

    // Source language change handler
    const sourceLanguageSelect = document.getElementById('sourceLanguage');
    sourceLanguageSelect.addEventListener('change', showTranslateButtonAfterChange);

    // Translate button handler
    translateBtn.addEventListener('click', startTranslation);

    // Preview button handler
    previewBtn.addEventListener('click', showPreview);

    // Toggle all columns button handler
    toggleAllColumns.addEventListener('click', function() {
        const checkboxContainer = document.getElementById('columnCheckboxes');
        if (!checkboxContainer) return;
        const checkboxes = checkboxContainer.querySelectorAll('input[type="checkbox"]');
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);

        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
        });

        // Update button text and icon safely
        let icon = this.querySelector('i');
        if (allChecked) {
            this.innerHTML = '<i class="fas fa-square me-1"></i>Select All';
        } else {
            this.innerHTML = '<i class="fas fa-check-square me-1"></i>Deselect All';
        }
        updateTranslateButton();
    });

    function handleFileSelect(file) {
        // Note: File cleanup now happens automatically on server-side during new upload
        // No need for manual cleanup here
        
        // Automatically detect file type by extension
        const allowedTypes = ['.xlsx', '.pptx', '.docx', '.pdf'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        fileType = fileExtension;
        if (!allowedTypes.includes(fileExtension)) {
            showAlert('Please select a valid file type: Excel (.xlsx), PowerPoint (.pptx), Word (.docx) or PDF (.pdf)', 'warning');
            return;
        }
        if (file.size > 50 * 1024 * 1024) {
            showAlert('File size must be less than 50MB', 'warning');
            return;
        }
        selectedFile = file;
        currentSessionId = null; // Reset session ID for new file
        translationCompleted = false;
        languageSettings.style.display = 'none';
        uploadArea.style.display = 'none';
        loadingSpinner.style.display = 'block';
        loadingText.textContent = 'Uploading and processing file...';
        // All logic is now automatic based on file extension
        if (fileExtension === '.xlsx' || fileExtension === '.pptx' || fileExtension === '.docx' || fileExtension === '.pdf') {
            uploadFileToServer(file);
        } else {
            setTimeout(() => {
                loadingSpinner.style.display = 'none';
                fileName.textContent = file.name;
                fileDetails.textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB • ${fileExtension.toUpperCase().substring(1)} file`;
                fileInfo.style.display = 'block';
                showFileOptions(fileExtension);
                updateTranslateButton();
            }, 800);
        }
    }

    function uploadFileToServer(file) {
        const formData = new FormData();
        formData.append('file', file);
        
        loadingText.textContent = 'Uploading file...';

        fetch('/translator/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.type == '.xlsx') {
                loadingText.textContent = 'Getting Excel table...';
                getExcelColumns();
                }
                else if (data.type == '.pptx') {
                loadingText.textContent = 'Getting PowerPoint slides...';
                // TODO
                }
                else if (data.type == '.docx') {
                loadingText.textContent = 'Getting Word paragraphs...';
                // TODO
                }
                else if (data.type == '.pdf'){
                loadingText.textContent = 'Getting PDF paragraphs...';
                }
                finishFileLoading(data.type);
            } else {
                loadingSpinner.style.display = 'none';
                uploadArea.style.display = 'block';
                
                // Reset file selection state
                selectedFile = null;
                currentSessionId = null;
                fileInput.value = '';
                
                showAlert('Error uploading file: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            loadingSpinner.style.display = 'none';
            uploadArea.style.display = 'block';
            
            // Reset file selection state
            selectedFile = null;
            currentSessionId = null;
            fileInput.value = '';
            
            showAlert('Error uploading file', 'danger');
        });
    }

    function finishFileLoading(fileExtension) {
        // Hide loading spinner and show file info
        loadingSpinner.style.display = 'none';
        // Show language settings now that upload/processing is done
        languageSettings.style.display = 'block';
        // Update file info display
        fileName.textContent = selectedFile.name;
        fileInfo.style.display = 'block';
        updateTranslateButton();
    }

    function getExcelColumns() {
        loadingText.textContent = 'Analyzing Excel columns...';
        
        fetch('/translator/api/columns', {
            method: 'GET'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Use generic file options function for all file types
                showFileOptions('.xlsx', data.columns);
                finishFileLoading();
            } else {
                console.error('Error getting columns:', data.error);
                console.log(data);
                
                // Handle specific file not found errors
                const errorMessage = data.error || 'Unknown error';
                if (errorMessage.includes('not found') || errorMessage.includes('No such file')) {
                    showAlert('The uploaded file is no longer available. Please upload the file again.', 'warning');
                    // Reset the interface to allow re-upload
                    selectedFile = null;
                    currentSessionId = null;
                    translationCompleted = false;
                    fileInfo.style.display = 'none';
                    uploadArea.style.display = 'block';
                    fileOptions.style.display = 'none';
                    languageSettings.style.display = 'none';
                    fileInput.value = '';
                } else {
                    showAlert('Error analyzing Excel file: ' + errorMessage, 'danger');
                    uploadArea.style.display = 'block';
                }
                
                loadingSpinner.style.display = 'none';
                updateTranslateButton();
            }
        })
        .catch(error => {
            console.error('Get columns error:', error);
            loadingSpinner.style.display = 'none';
            uploadArea.style.display = 'block';
            showAlert('Error analyzing Excel file', 'danger');
        });
    }

    function showFileOptions(fileExtension, options = null) {
        const fileOptionsHeader = document.getElementById('fileOptionsHeader');
        const fileOptionsDescription = document.getElementById('fileOptionsDescription');
        optionCheckboxes.innerHTML = '';
        let opts = [];
        if (fileExtension === '.xlsx') {
            fileOptionsHeader.textContent = 'Select columns to translate:';
            fileOptionsDescription.textContent = 'Select which columns you want to translate.';
            opts = options || [];
            toggleAllColumns.style.display = opts.length > 1 ? 'inline-block' : 'none';
        } else if (fileExtension === '.docx') {
            fileOptionsHeader.textContent = 'Select paragraphs to translate:';
            fileOptionsDescription.textContent = 'Select which paragraphs you want to translate.';
            opts = ['All Paragraphs'];
            toggleAllColumns.style.display = 'none';
        } else if (fileExtension === '.pptx') {
            fileOptionsHeader.textContent = 'Select slides to translate:';
            fileOptionsDescription.textContent = 'Select which slides you want to translate.';
            opts = ['All Slides'];
            toggleAllColumns.style.display = 'none';
        } else if (fileExtension == '.pdf'){
            ileOptionsHeader.textContent = 'Select paragraphs to translate:';
            fileOptionsDescription.textContent = 'Select which paragraphs you want to translate.';
            opts = ['All Paragraphs'];
            toggleAllColumns.style.display = 'none';
        }
        const checkboxContainer = document.createElement('div');
        checkboxContainer.className = 'row';
        checkboxContainer.id = 'columnCheckboxes'; // Assign id for later reference
        opts.forEach((option, index) => {
            const colDiv = document.createElement('div');
            colDiv.className = 'col-md-6 column-checkbox';
            colDiv.innerHTML = `
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${option}" id="opt${index}" checked>
                    <label class="form-check-label" for="opt${index}">
                        ${option}
                    </label>
                </div>
            `;
            checkboxContainer.appendChild(colDiv);
        });
        optionCheckboxes.appendChild(checkboxContainer);
        fileOptions.style.display = 'block';
        languageSettings.style.display = 'block';
        translateBtn.hidden = false;
    }
    

    
    function getLanguageName(code) {
        const languages = {
            'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
            'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ja': 'Japanese',
            'ko': 'Korean', 'zh': 'Chinese', 'ar': 'Arabic', 'hi': 'Hindi',
            'th': 'Thai', 'vi': 'Vietnamese', 'nl': 'Dutch', 'sv': 'Swedish',
            'no': 'Norwegian', 'da': 'Danish', 'fi': 'Finnish', 'pl': 'Polish'
        };
        return languages[code] || code;
    }

    function getSelectedTargetLanguages() {
        // Returns an array of selected language codes from Select2
        return $('#targetLanguage').val() || [];
    }

    let translationCompleted = false; // Track if translation was completed

    function updateTranslateButton() {
        const hasFile = selectedFile !== null;
        const selectedLangs = getSelectedTargetLanguages();

        // Hide button if translation was completed and no changes made
        if (translationCompleted) {
            translateBtn.style.display = 'none';
            return;
        }

        translateBtn.hidden = !(hasFile);
        translateBtn.disabled = !(hasFile && selectedLangs.length > 0);
        translateBtn.style.display = hasFile ? 'inline-block' : 'none';

        // Update preview button state
        previewBtn.hidden = !(hasFile);
        previewBtn.disabled = !(hasFile && selectedLangs.length > 0);
        previewBtn.style.display = hasFile ? 'inline-block' : 'none';
    }

    function showTranslateButtonAfterChange() {
        // Show translate button again when user makes changes
        if (translationCompleted) {
            translationCompleted = false;
            translateBtn.style.display = 'inline-block';
            updateTranslateButton();
        }
    }

    function startTranslation() {
        const selectedLangs = getSelectedTargetLanguages();
        if (!selectedFile || selectedLangs.length === 0) {
            alert('Please select a file and at least one target language');
            return;
        }
        
        // Remove any existing download links immediately when translation starts
        const existingLinks = document.querySelectorAll('.download-link-custom, .new-translation-btn');
        existingLinks.forEach(link => link.remove());
        
        // Get selected columns for Excel files
        let selectedColumns = [];
        if (fileType === '.xlsx') {
            const checkboxes = document.getElementById('columnCheckboxes').querySelectorAll('input[type="checkbox"]:checked');
            selectedColumns = Array.from(checkboxes).map(cb => cb.value);
        } else if (fileType === '.docx') {
            selectedColumns = ['All Paragraphs'];
        } else if (fileType === '.pptx') {
            selectedColumns = ['All Slides'];
        } else if (fileType === '.pdf'){
            selectedColumns = ['All Paragraphs'];
        }

        translateBtn.disabled = true;
        translateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Translating...';
        
        // Disable the remove file button during translation
        removeFile.disabled = true;
        removeFile.style.opacity = '0.5';
        removeFile.style.cursor = 'not-allowed';

        // Generate session ID for tracking
        const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        currentSessionId = sessionId; // Store the session ID

        // Start translation
        const fileContext = document.getElementById('fileContext').value;
        const translationData = {
            target_languages: selectedLangs,
            source_language: document.getElementById('sourceLanguage').value,
            selected_columns: selectedColumns,
            file_type: '.' + selectedFile.name.split('.').pop().toLowerCase(),
            original_filename: selectedFile.name,
            file_context: fileContext,
            session_id: sessionId
        };

        // Start the translation request
        fetch('/translator/api/translate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(translationData)
        })
        .then(response => response.json())
        .then(data => {
            console.log('Translation response received:', data); // Debug log

            translateBtn.disabled = false;
            translateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Start Translation';
            
            // Re-enable the remove file button after translation
            removeFile.disabled = false;
            removeFile.style.opacity = '1';
            removeFile.style.cursor = 'pointer';

            if (data.success) {
                // Mark translation as completed IMMEDIATELY to prevent cleanup
                translationCompleted = true;
                console.log('Translation marked as completed, preventing cleanup');
                
                translateBtn.style.display = 'none';

                // Show success message with information about partial failures if applicable
                const requestedLangs = getSelectedTargetLanguages();
                const successfulLangs = data.languages || [];
                
                if (successfulLangs.length === requestedLangs.length) {
                    showAlert('Translation completed successfully!', 'success');
                } else if (successfulLangs.length > 0) {
                    const failedLangs = requestedLangs.filter(lang => !successfulLangs.includes(lang));
                    const failedLangNames = failedLangs.map(code => getLanguageName(code)).join(', ');
                    showAlert(`Translation partially completed. ${failedLangNames} translation(s) failed, but others succeeded.`, 'warning');
                } else {
                    showAlert('Translation failed for all languages.', 'danger');
                    return;
                }

                // Create download link only if we have successful translations
                if (successfulLangs.length > 0) {
                    const downloadLink = document.createElement('a');
                    downloadLink.className = 'btn btn-success btn-lg mt-3 download-link-custom';
                    downloadLink.style.display = 'block';
                    downloadLink.style.margin = '20px auto';
                    downloadLink.style.width = 'fit-content';

                    if (data.zip_file && successfulLangs.length > 1) {
                        // Multiple successful languages - show zip download
                        const zipFileParam = encodeURIComponent(data.zip_file);
                        downloadLink.href = `/translator/api/download/current?zip_file=${zipFileParam}`;
                        downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download All Translations (ZIP)';
                    } else if (successfulLangs.length === 1) {
                        // Single successful language - show single file download
                        const langParam = encodeURIComponent(successfulLangs[0]);
                        downloadLink.href = `/translator/api/download/current?lang=${langParam}`;
                        downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                    } else {
                        // Multiple languages but no zip (shouldn't happen, but fallback)
                        const langParam = encodeURIComponent(successfulLangs[0]);
                        downloadLink.href = `/translator/api/download/current?lang=${langParam}`;
                        downloadLink.innerHTML = '<i class="fas fa-download me-2"></i>Download Translated File';
                    }

                    // Insert download link after the translate button
                    const translateBtnElement = document.getElementById('translateBtn');
                    translateBtnElement.parentNode.insertBefore(downloadLink, translateBtnElement.nextSibling);
                    
                    // Add a "Translate Another File" button
                    const newTranslationBtn = document.createElement('button');
                    newTranslationBtn.className = 'btn btn-outline-primary btn-lg mt-3 new-translation-btn';
                    newTranslationBtn.style.display = 'block';
                    newTranslationBtn.style.margin = '10px auto 0';
                    newTranslationBtn.style.width = 'fit-content';
                    newTranslationBtn.innerHTML = '<i class="fas fa-plus me-2"></i>Translate Another File';
                    newTranslationBtn.addEventListener('click', function() {
                        // Reset UI for new translation - cleanup handled by server on next upload
                        selectedFile = null;
                        currentSessionId = null;
                        translationCompleted = false;
                        fileInfo.style.display = 'none';
                        uploadArea.style.display = 'block';
                        fileOptions.style.display = 'none';
                        languageSettings.style.display = 'none';
                        fileInput.value = '';
                        // Remove all custom buttons and download links
                        const existingDownloadLinks = document.querySelectorAll('.download-link-custom, .new-translation-btn');
                        existingDownloadLinks.forEach(link => link.remove());
                        updateTranslateButton();
                    });
                    
                    // Insert the new translation button after the download link
                    downloadLink.parentNode.insertBefore(newTranslationBtn, downloadLink.nextSibling);
                }
            } else {
                // Handle specific file not found errors
                const errorMessage = data.error || 'Unknown error';
                if (errorMessage.includes('Package not found') || errorMessage.includes('file not found') || errorMessage.includes('No such file')) {
                    showAlert('The uploaded file is no longer available. Please upload the file again.', 'warning');
                    // Reset the interface to allow re-upload
                    selectedFile = null;
                    currentSessionId = null;
                    translationCompleted = false;
                    fileInfo.style.display = 'none';
                    uploadArea.style.display = 'block';
                    fileOptions.style.display = 'none';
                    languageSettings.style.display = 'none';
                    fileInput.value = '';
                    updateTranslateButton();
                } else {
                    showAlert('Translation failed: ' + errorMessage, 'danger');
                }
            }
        })
        .catch(error => {
            console.error('Translation error:', error);
            console.log('Error details:', error.message, error.stack); // More detailed error logging
            translateBtn.disabled = false;
            translateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>Start Translation';
            showAlert('Translation failed due to network error', 'danger');

            // Re-enable the remove file button after translation error
            removeFile.disabled = false;
            removeFile.style.opacity = '1';
            removeFile.style.cursor = 'pointer';
            
            // Note: File cleanup now only happens on new file upload
            // Removed cleanup here to preserve user files
            currentSessionId = null;
        });
    }

    function showPreview() {
        const selectedLangs = getSelectedTargetLanguages();
        if (!selectedFile || selectedLangs.length === 0) {
            alert('Please select a file and at least one target language');
            return;
        }

        // For preview, we'll use the first selected language
        const targetLanguage = selectedLangs[0];

        // Get selected columns/options
        let selectedColumns = [];
        if (fileType === '.xlsx') {
            const checkboxes = document.querySelectorAll('#columnCheckboxes input[type="checkbox"]:checked');
            selectedColumns = Array.from(checkboxes).map(cb => cb.value);
        } else if (fileType === '.docx') {
            selectedColumns = ['All Paragraphs'];
        } else if (fileType === '.pptx') {
            selectedColumns = ['All Slides'];
        } else if (fileType === '.pdf') {
            selectedColumns = ['All Paragraphs'];
        }

        // Disable preview button during request
        previewBtn.disabled = true;
        previewBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating Preview...';

        const fileContext = document.getElementById('fileContext').value;
        const previewData = {
            target_language: targetLanguage,
            file_type: fileType,
            selected_columns: selectedColumns,
            file_context: fileContext,
            max_items: 10
        };

        fetch('/translator/api/preview', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(previewData)
        })
        .then(response => response.json())
        .then(data => {
            console.log('Preview response received:', data);

            previewBtn.disabled = false;
            previewBtn.innerHTML = '<i class="fas fa-eye me-2"></i>Preview Translation';

            if (data.success) {
                displayPreviewModal(data.data, targetLanguage);
            } else {
                showAlert('Preview generation failed: ' + (data.error || 'Unknown error'), 'danger');
            }
        })
        .catch(error => {
            console.error('Preview error:', error);
            previewBtn.disabled = false;
            previewBtn.innerHTML = '<i class="fas fa-eye me-2"></i>Preview Translation';
            showAlert('Preview failed due to network error', 'danger');
        });
    }

    function displayPreviewModal(previewData, targetLanguage) {
        const previewContent = document.getElementById('previewContent');

        let html = `
            <div class="preview-summary mb-4">
                <h6><i class="fas fa-info-circle me-2"></i>Preview Summary</h6>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Target Language:</strong> ${targetLanguage}</p>
                        <p><strong>File Type:</strong> ${previewData.file_info ? Object.keys(previewData.file_info).length : 'Unknown'} properties detected</p>
                    </div>
                    <div class="col-md-6">`;

        if (previewData.overall_statistics) {
            html += `
                        <p><strong>Estimated Elements:</strong> ${previewData.overall_statistics.estimated_translation_elements || previewData.overall_statistics.total_cells_to_translate || 'N/A'}</p>
                        <p><strong>Content Types:</strong> ${previewData.overall_statistics.content_types ? previewData.overall_statistics.content_types.join(', ') : 'N/A'}</p>`;
        }

        html += `
                    </div>
                </div>
            </div>`;

        if (previewData.sample_translations && previewData.sample_translations.length > 0) {
            html += `
                <div class="preview-samples">
                    <h6><i class="fas fa-language me-2"></i>Sample Translations</h6>
                    <div class="accordion" id="previewAccordion">`;

            previewData.sample_translations.forEach((sample, index) => {
                html += `
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="heading${index}">
                            <button class="accordion-button ${index === 0 ? '' : 'collapsed'}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${index}" aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="collapse${index}">
                                Sample ${index + 1}${sample.column ? ` - ${sample.column}` : ''}${sample.slide ? ` - Slide ${sample.slide}` : ''}
                            </button>
                        </h2>
                        <div id="collapse${index}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" aria-labelledby="heading${index}" data-bs-parent="#previewAccordion">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-muted">Original:</h6>
                                        <div class="border p-2 bg-light rounded">${sample.original}</div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-muted">Translated:</h6>
                                        <div class="border p-2 bg-success bg-opacity-10 rounded">${sample.translated}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>`;
            });

            html += `
                    </div>
                </div>`;
        } else {
            html += `
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    No sample translations available. The preview shows file structure only.
                </div>`;
        }

        if (previewData.samples && previewData.samples.length > 0) {
            html += `
                <div class="preview-structure mt-4">
                    <h6><i class="fas fa-list me-2"></i>Content Structure</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Content</th>
                                    <th>Type</th>
                                    <th>Length</th>
                                </tr>
                            </thead>
                            <tbody>`;

            previewData.samples.slice(0, 5).forEach(sample => {
                const content = typeof sample === 'object' ? (sample.text || JSON.stringify(sample.content || sample)) : sample;
                const truncatedContent = content.length > 100 ? content.substring(0, 100) + '...' : content;
                html += `
                                <tr>
                                    <td>${truncatedContent}</td>
                                    <td>${sample.type || sample.column || 'Text'}</td>
                                    <td>${sample.length || content.length}</td>
                                </tr>`;
            });

            html += `
                            </tbody>
                        </table>
                    </div>
                </div>`;
        }

        previewContent.innerHTML = html;

        // Show the modal with manual backdrop control
        const previewModalElement = document.getElementById('previewModal');

        // Close any existing modal first
        if (currentPreviewModal) {
            try {
                currentPreviewModal.dispose();
            } catch (e) {
                console.log('Error disposing previous modal:', e);
            }
            currentPreviewModal = null;
        }

        // Force clean any existing backdrops before creating new modal
        document.querySelectorAll('.modal-backdrop').forEach(backdrop => backdrop.remove());

        // Use Bootstrap's built-in backdrop but with better cleanup
        const previewModal = new bootstrap.Modal(previewModalElement, {
            backdrop: true, // Let Bootstrap handle backdrop creation
            keyboard: true,
            focus: true
        });

        // Store the modal instance
        currentPreviewModal = previewModal;

        // Handle modal events properly
        previewModalElement.addEventListener('shown.bs.modal', function () {
            // Reset scroll position to top
            const modalBody = previewModalElement.querySelector('.modal-body');
            if (modalBody) {
                modalBody.scrollTop = 0;
            }
            // Ensure modal is focusable
            previewModalElement.focus();
        });

        previewModalElement.addEventListener('hidden.bs.modal', function () {
            console.log('Modal hidden event triggered - starting cleanup');

            // Use setTimeout to ensure Bootstrap has finished its cleanup
            setTimeout(() => {
                // Clean up any remaining backdrops (Bootstrap should handle this, but sometimes doesn't)
                const allBackdrops = document.querySelectorAll('.modal-backdrop');
                if (allBackdrops.length > 0) {
                    console.log(`Found ${allBackdrops.length} remaining backdrops, removing them`);
                    allBackdrops.forEach((backdrop, index) => {
                        console.log(`Removing backdrop ${index + 1}:`, backdrop.className);
                        backdrop.remove();
                    });
                }

                // Clean up body styles and classes (Bootstrap should handle this too)
                document.body.classList.remove('modal-open');
                document.body.style.removeProperty('overflow');
                document.body.style.removeProperty('padding-right');
                document.body.style.removeProperty('margin-right');

                // Ensure modal element is properly reset
                previewModalElement.classList.remove('show');
                previewModalElement.style.display = 'none';
                previewModalElement.setAttribute('aria-hidden', 'true');
                previewModalElement.removeAttribute('aria-modal');

                console.log('✅ Preview modal cleanup completed');
            }, 150); // Give Bootstrap time to clean up first

            // Remove escape key handler
            if (currentPreviewModal && currentPreviewModal._escapeHandler) {
                document.removeEventListener('keydown', currentPreviewModal._escapeHandler);
            }

            // Clear the modal instance reference
            currentPreviewModal = null;
        });

        // Show the modal with error handling
        try {
            previewModal.show();
            console.log('✅ Preview modal opened successfully');
        } catch (error) {
            console.error('❌ Error opening preview modal:', error);
            // Fallback: show modal manually
            previewModalElement.style.display = 'block';
            previewModalElement.classList.add('show');
            previewModalElement.setAttribute('aria-modal', 'true');
            previewModalElement.removeAttribute('aria-hidden');

            // Add backdrop manually as fallback
            const fallbackBackdrop = document.createElement('div');
            fallbackBackdrop.className = 'modal-backdrop fade show';
            fallbackBackdrop.style.zIndex = '1040';
            document.body.appendChild(fallbackBackdrop);
            document.body.classList.add('modal-open');
        }

        // Handle "Proceed with Full Translation" button
        document.getElementById('proceedWithTranslation').onclick = function() {
            console.log('Proceed button clicked - closing modal');
            previewModal.hide();
            setTimeout(() => startTranslation(), 300); // Small delay to ensure modal is fully closed
        };

        // Ensure all close buttons work properly
        const closeButtons = previewModalElement.querySelectorAll('[data-bs-dismiss="modal"], .btn-close');
        closeButtons.forEach(button => {
            button.onclick = function(e) {
                e.preventDefault();
                console.log('Close button clicked');
                previewModal.hide();
            };
        });

        // Bootstrap will handle backdrop clicks automatically

        // Handle escape key
        const escapeHandler = function(e) {
            if (e.key === 'Escape' && previewModalElement.classList.contains('show')) {
                console.log('Escape key pressed - closing modal');
                previewModal.hide();
            }
        };
        document.addEventListener('keydown', escapeHandler);

        // Store escape handler for cleanup
        previewModal._escapeHandler = escapeHandler;
    }

    // Global function to force-close any stuck modals (for debugging)
    window.forceCloseModal = function() {
        console.log('🚨 FORCE CLOSING MODAL - Emergency cleanup...');

        // Close current preview modal if exists
        if (currentPreviewModal) {
            try {
                currentPreviewModal.hide();
            } catch (e) {
                console.log('Error hiding modal:', e);
            }
            currentPreviewModal = null;
        }

        // Clean up any stuck modal elements
        document.querySelectorAll('.modal.show').forEach(modal => {
            modal.classList.remove('show');
            modal.style.display = 'none';
            modal.setAttribute('aria-hidden', 'true');
            modal.removeAttribute('aria-modal');
        });

        // AGGRESSIVE backdrop removal - try multiple selectors
        const backdropSelectors = [
            '.modal-backdrop',
            '.modal-backdrop.fade',
            '.modal-backdrop.show',
            '.modal-backdrop.fade.show',
            '[class*="modal-backdrop"]',
            '[class*="backdrop"]'
        ];

        let totalRemoved = 0;
        backdropSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (element.classList.contains('modal-backdrop') || element.className.includes('backdrop')) {
                    console.log(`🗑️ Removing: ${element.className}`);
                    element.remove();
                    totalRemoved++;
                }
            });
        });

        // Clean up body classes and styles
        document.body.classList.remove('modal-open');
        document.body.style.removeProperty('overflow');
        document.body.style.removeProperty('padding-right');

        // Reset any other potential modal-related styles
        document.body.style.removeProperty('margin-right');
        document.body.removeAttribute('data-bs-overflow');
        document.body.removeAttribute('data-bs-padding-right');

        console.log(`✅ Modal force-closed! Removed ${totalRemoved} backdrop elements.`);
        console.log('Page should now be interactive again.');

        return totalRemoved;
    };
});
</script>
{% endblock %}
