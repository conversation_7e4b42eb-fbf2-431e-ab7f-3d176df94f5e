import json
import os
import sys
from pathlib import Path
from typing import List, Dict, Optional
import logging
from dataclasses import dataclass

# Libraries for PowerPoint
from openai import AzureOpenAI
from pptx import Presentation
from pptx.shapes.base import BaseShape
from pptx.text.text import TextFrame
from pptx.shapes.shapetree import SlideShapes
from pptx.enum.shapes import MSO_SHAPE_TYPE

# LangChain imports
from langchain.schema import BaseMessage, HumanMessage
from langchain.prompts import PromptTemplate
from langchain.chains.llm import LLMChain
from langchain.schema.runnable import RunnablePassthrough
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import AzureChatOpenAI as LangChainAzureOpenAI
from langchain_core.output_parsers import StrOutputParser


from src.agents.eproexcella_agent.models.testual import TextTranslator
from src.backend.blueprints.translator_bot.translator import Translator
from utils.core import get_logger
from config.config import EproExcelLaConfig
from utils.core import Singleton

# Logging configuration
FILE_DIR = os.path.dirname(__file__)
logger = get_logger(__file__)

class PPTXHandler:
    
    def __init__(self, pptx_path, file_context=None):
        self.pptx_path = pptx_path
        self.file_context = file_context

    def _process_shape_recursively(self, shape, target_lang: str, depth: int = 0) -> None:
        indent = "  " * depth
        logger.debug(f"{indent}Processing shape type: {shape.shape_type}")
        
        # Handle grouped shapes
        if shape.shape_type == MSO_SHAPE_TYPE.GROUP:
            logger.info(f"{indent}Found GROUP shape with {len(shape.shapes)} child shapes")
            for child_shape in shape.shapes:
                self._process_shape_recursively(child_shape, target_lang, depth + 1)

        # Handle table shapes
        elif shape.shape_type == MSO_SHAPE_TYPE.TABLE:
            logger.info(f"{indent}Found TABLE shape, translating table content")
            self._translate_table_content(shape.table, target_lang)

        # Handle shapes with text_frame
        elif shape.has_text_frame:
            logger.debug(f"{indent}Found text shape, translating content")
            self._translate_text_frame_runs_preserve_layout(shape.text_frame, target_lang)

        # Fallback: shapes with `.text` but no `.text_frame`
        elif hasattr(shape, "text") and shape.text.strip():
            logger.debug(f"{indent}Fallback: shape has .text but no .text_frame — translating")
            try:
                translated_text = self.batch_translate([shape.text], target_lang)[0]
                shape.text = translated_text
            except Exception as e:
                logger.warning(f"{indent}Could not update shape.text: {e}")

        else:
            logger.debug(f"{indent}Shape type {shape.shape_type} - no translatable content")

    def _translate_table_content(self, table, target_lang: str) -> None:
        logger.info(f"Translating table with {len(table.rows)} rows and {len(table.columns)} columns")
        all_runs = []
        run_texts = []
        for row in table.rows:
            for cell in row.cells:
                if cell.text and cell.text.strip():
                    for paragraph in cell.text_frame.paragraphs:
                        for run in paragraph.runs:
                            all_runs.append(run)
                            run_texts.append(run.text)
        if not run_texts:
            return
        translated_texts = self.batch_translate(run_texts, target_lang)
        for run, translated_text in zip(all_runs, translated_texts):
            run.text = translated_text

    def _translate_slide(self, slide, target_lang: str) -> None:
        logger.info(f"Processing slide with {len(slide.shapes)} top-level shapes")
        shape_counts = {}
        for shape in slide.shapes:
            shape_type = shape.shape_type
            shape_counts[shape_type] = shape_counts.get(shape_type, 0) + 1
        if MSO_SHAPE_TYPE.GROUP in shape_counts:
            logger.info(f"Slide contains {shape_counts[MSO_SHAPE_TYPE.GROUP]} grouped element(s)")
        if MSO_SHAPE_TYPE.TABLE in shape_counts:
            logger.info(f"Slide contains {shape_counts[MSO_SHAPE_TYPE.TABLE]} table(s)")
        for shape in slide.shapes:
            self._process_shape_recursively(shape, target_lang)

    def batch_translate(self, texts: List[str], target_lang: str, batch_size: int = 50) -> List[str]:
        all_translations = []
        translator = Translator()
        prompt = translator.get_default_prompt(target_lang)
        for i in range(0, len(texts), batch_size):
            batch = texts[i:i+batch_size]
            non_empty_batch = []
            non_empty_indices = []
            for j, text in enumerate(batch):
                if text.strip():
                    non_empty_batch.append(text)
                    non_empty_indices.append(j)
            if not non_empty_batch:
                all_translations.extend(batch)
                continue
            dict_data = {str(k+1): text for k, text in enumerate(non_empty_batch)}
            try:
                result = translator.submit_to_gpt(dict_data, prompt) if self.file_context is None else translator.submit_to_gpt(dict_data, prompt, self.file_context)
                if result == '{}':
                    translated_batch = batch[:]
                else:
                    translated_texts = json.loads(result)
                    translated_batch = batch[:]
                    for k, original_idx in enumerate(non_empty_indices):
                        key = str(k+1)
                        if key in translated_texts:
                            translated_batch[original_idx] = translated_texts[key]
                all_translations.extend(translated_batch)
            except Exception as e:
                logger.error(f"Translation batch error: {e}")
                all_translations.extend(batch)
        return all_translations

    def _translate_text_frame_runs_preserve_layout(self, text_frame, target_lang):
        for paragraph in text_frame.paragraphs:
            runs = paragraph.runs
            if not runs:
                continue
            original_texts = [run.text for run in runs]
            combined_text = " ".join(original_texts).strip()
            if not combined_text:
                continue
            translated_text = self.batch_translate([combined_text], target_lang)[0]
            total_original_chars = sum(len(t) for t in original_texts)
            if total_original_chars == 0:
                continue
            translated_fragments = []
            start_idx = 0
            for original_text in original_texts:
                proportion = len(original_text) / total_original_chars
                chars_to_take = round(proportion * len(translated_text))
                translated_fragments.append(translated_text[start_idx:start_idx + chars_to_take])
                start_idx += chars_to_take
            if start_idx < len(translated_text):
                translated_fragments[-1] += translated_text[start_idx:]
            for run, new_text in zip(runs, translated_fragments):
                run.text = new_text

    def get_slide_statistics(self, slide) -> Dict[str, int]:
        stats = {
            'total_shapes': 0,
            'grouped_shapes': 0,
            'tables': 0,
            'text_shapes': 0,
            'nested_depth': 0
        }
        def count_shapes_recursive(shapes, depth=0):
            stats['nested_depth'] = max(stats['nested_depth'], depth)
            for shape in shapes:
                stats['total_shapes'] += 1
                if shape.shape_type == MSO_SHAPE_TYPE.GROUP:
                    stats['grouped_shapes'] += 1
                    count_shapes_recursive(shape.shapes, depth + 1)
                elif shape.shape_type == MSO_SHAPE_TYPE.TABLE:
                    stats['tables'] += 1
                elif shape.has_text_frame and shape.text.strip():
                    stats['text_shapes'] += 1
        count_shapes_recursive(slide.shapes)
        return stats

    def translate_presentation(self, target_language: str) -> str:
        try:
            logger.info(f"Loading presentation: {self.pptx_path}")
            presentation = Presentation(self.pptx_path)
            total_slides = len(presentation.slides)
            logger.info(f"Starting translation of {total_slides} slides")
            for slide_num, slide in enumerate(presentation.slides, 1):
                logger.info(f"Translating slide {slide_num}/{total_slides}")
                slide_stats = self.get_slide_statistics(slide)
                logger.info(f"Slide {slide_num} statistics: {slide_stats}")
                self._translate_slide(slide, target_language)
                if hasattr(slide, 'notes_slide') and slide.notes_slide:
                    logger.info(f"Processing notes for slide {slide_num}")
                    notes_runs = []
                    notes_texts = []
                    for shape in slide.notes_slide.shapes:
                        if shape.has_text_frame:
                            for paragraph in shape.text_frame.paragraphs:
                                for run in paragraph.runs:
                                    notes_runs.append(run)
                                    notes_texts.append(run.text)
                    if notes_texts:
                        translated_notes = self.batch_translate(notes_texts, target_language)
                        for run, translated_text in zip(notes_runs, translated_notes):
                            run.text = translated_text
            return presentation
        except Exception as e:
            logger.error(f"Error during translation: {e}")
            return None

    def write_result_to_file(self, presentation: Presentation, lang: str):
        logger.info(f"Writing results to PowerPoint for language: {lang}")
        original_base, original_ext = os.path.splitext(self.pptx_path)
        translated_path = f"{original_base}_{lang}{original_ext}"

        # Ensure the directory exists
        directory = os.path.dirname(translated_path)
        if not os.path.exists(directory):
            logger.info(f"Creating directory: {directory}")
            os.makedirs(directory, exist_ok=True)

        try:
            presentation.save(translated_path)
            logger.info(f"Successfully saved translated PowerPoint file: {translated_path}")
        except Exception as e:
            logger.error(f"Error saving translated PowerPoint file: {e}")
            raise

    def get_preview_data(self, selected_columns=None, max_items=10):
        """
        Get preview data for PowerPoint presentation translation.

        Args:
            selected_columns (list): Not used for PowerPoint files (kept for interface compatibility)
            max_items (int): Maximum number of text samples to include in preview

        Returns:
            dict: Preview data containing sample content and metadata
        """
        try:
            if not os.path.exists(self.pptx_path):
                raise FileNotFoundError(f"PowerPoint file not found: {self.pptx_path}")

            presentation = Presentation(self.pptx_path)

            # Prepare preview data
            preview_data = {
                'file_info': {
                    'total_slides': len(presentation.slides),
                    'slide_layouts': len(presentation.slide_layouts),
                    'slide_masters': len(presentation.slide_masters)
                },
                'samples': [],
                'statistics': {
                    'total_text_shapes': 0,
                    'total_text_elements': 0,
                    'slides_with_notes': 0
                }
            }

            sample_count = 0
            total_text_shapes = 0
            total_text_elements = 0
            slides_with_notes = 0

            # Process each slide
            for slide_idx, slide in enumerate(presentation.slides):
                if sample_count >= max_items:
                    break

                slide_text_count = 0

                # Process shapes in slide
                for shape in slide.shapes:
                    if sample_count >= max_items:
                        break

                    if shape.has_text_frame:
                        total_text_shapes += 1
                        text_frame = shape.text_frame

                        for paragraph in text_frame.paragraphs:
                            for run in paragraph.runs:
                                text = run.text.strip()
                                if text and len(text) > 3:  # Only include meaningful text
                                    total_text_elements += 1
                                    slide_text_count += 1

                                    if sample_count < max_items:
                                        preview_data['samples'].append({
                                            'text': text,
                                            'type': 'text_run',
                                            'slide': slide_idx + 1,
                                            'shape_type': str(shape.shape_type),
                                            'length': len(text),
                                            'word_count': len(text.split())
                                        })
                                        sample_count += 1

                # Check for notes
                if slide.has_notes_slide:
                    has_notes_text = False
                    for shape in slide.notes_slide.shapes:
                        if shape.has_text_frame:
                            for paragraph in shape.text_frame.paragraphs:
                                for run in paragraph.runs:
                                    notes_text = run.text.strip()
                                    if notes_text and len(notes_text) > 3:
                                        has_notes_text = True
                                        total_text_elements += 1

                                        if sample_count < max_items:
                                            preview_data['samples'].append({
                                                'text': notes_text,
                                                'type': 'notes',
                                                'slide': slide_idx + 1,
                                                'length': len(notes_text),
                                                'word_count': len(notes_text.split())
                                            })
                                            sample_count += 1

                    if has_notes_text:
                        slides_with_notes += 1

            # Update statistics
            preview_data['statistics'] = {
                'total_text_shapes': total_text_shapes,
                'total_text_elements': total_text_elements,
                'slides_with_notes': slides_with_notes,
                'average_text_per_slide': total_text_elements / len(presentation.slides) if presentation.slides else 0
            }

            # Add overall statistics
            preview_data['overall_statistics'] = {
                'estimated_translation_elements': total_text_elements,
                'estimated_translation_time': f"{total_text_elements * 1.5} seconds",  # Rough estimate
                'content_types': []
            }

            # Identify content types present
            if total_text_shapes > 0:
                preview_data['overall_statistics']['content_types'].append('text_shapes')
            if slides_with_notes > 0:
                preview_data['overall_statistics']['content_types'].append('slide_notes')

            logger.info(f"Generated preview for PowerPoint: {len(presentation.slides)} slides, {len(preview_data['samples'])} samples")
            return preview_data

        except Exception as e:
            logger.error(f"Error generating PowerPoint preview: {e}")
            return {
                'error': str(e),
                'file_info': {},
                'samples': [],
                'statistics': {}
            }