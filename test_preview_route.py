#!/usr/bin/env python3
"""
Simple test script to verify the preview route is properly registered.
Run this to check if the Flask app can start with the new preview functionality.
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_route_registration():
    """Test that the preview route is properly registered."""
    try:
        # Import the Flask app
        from src import create_app
        
        app = create_app()
        
        with app.app_context():
            # List all routes to verify our preview route is registered
            routes = []
            for rule in app.url_map.iter_rules():
                routes.append({
                    'endpoint': rule.endpoint,
                    'methods': list(rule.methods),
                    'rule': rule.rule
                })
            
            # Look for our preview route
            preview_routes = [r for r in routes if 'preview' in r['rule']]
            
            print("🔍 Found preview-related routes:")
            for route in preview_routes:
                print(f"  - {route['rule']} [{', '.join(route['methods'])}] -> {route['endpoint']}")
            
            # Check specifically for our new route
            translator_preview_route = [r for r in routes if r['rule'] == '/translator/api/preview']
            
            if translator_preview_route:
                print("✅ SUCCESS: Translation preview route is properly registered!")
                print(f"   Route: {translator_preview_route[0]['rule']}")
                print(f"   Methods: {translator_preview_route[0]['methods']}")
                print(f"   Endpoint: {translator_preview_route[0]['endpoint']}")
                return True
            else:
                print("❌ ERROR: Translation preview route not found!")
                print("   Expected route: /translator/api/preview")
                
                # Show all translator routes for debugging
                translator_routes = [r for r in routes if '/translator' in r['rule']]
                print("\n🔍 All translator routes found:")
                for route in translator_routes:
                    print(f"  - {route['rule']} [{', '.join(route['methods'])}] -> {route['endpoint']}")
                
                return False
                
    except Exception as e:
        print(f"❌ ERROR: Failed to test route registration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_imports():
    """Test that all required modules can be imported."""
    try:
        print("🧪 Testing imports...")
        
        # Test translation service import
        from src.backend.blueprints.translator_bot.translation_service import TranslationService
        print("✅ TranslationService imported successfully")
        
        # Test file handlers
        from src.backend.blueprints.translator_bot.excel_handler import ExcelHandler
        print("✅ ExcelHandler imported successfully")
        
        from src.backend.blueprints.translator_bot.word_handler import WordHandler
        print("✅ WordHandler imported successfully")
        
        from src.backend.blueprints.translator_bot.pptx_handler import PPTXHandler
        print("✅ PPTXHandler imported successfully")
        
        # Test routes
        from src.backend.blueprints.translator_bot.translation_routes import translator_bot_routes
        print("✅ translator_bot_routes imported successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ ERROR: Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Testing Translation Preview Feature")
    print("=" * 50)
    
    # Test imports first
    if not test_imports():
        print("\n❌ Import tests failed. Please check your Python environment and dependencies.")
        return False
    
    print("\n" + "=" * 50)
    
    # Test route registration
    if not test_route_registration():
        print("\n❌ Route registration tests failed. The preview route may not be properly registered.")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 ALL TESTS PASSED!")
    print("\nThe translation preview feature should be working correctly.")
    print("Make sure to restart your Flask application to pick up the new routes.")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
