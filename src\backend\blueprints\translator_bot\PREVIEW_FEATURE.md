# Translation Preview Feature

## Overview

The Translation Preview feature allows users to see a sample of how their document will be translated before committing to the full translation process. This helps users:

- Verify translation quality and accuracy
- Adjust file context or settings if needed
- Understand what content will be translated
- Make informed decisions before processing large files

## How It Works

### Backend Implementation

1. **Abstract Base Class**: Added `get_preview_data()` method to `TranslationFileHandler` abstract base class
2. **File Handlers**: Implemented preview functionality in:
   - `ExcelHandler`: Shows sample data from selected columns
   - `WordHandler`: Shows sample paragraphs and document structure
   - `PPTXHandler`: Shows sample text from slides and notes
3. **Translation Service**: Added `get_preview()` method that:
   - Generates preview data using appropriate file handler
   - Optionally translates a few samples using the selected target language
   - Returns structured preview information

### Frontend Implementation

1. **Preview Button**: Added alongside the "Start Translation" button
2. **Preview Modal**: Bootstrap modal displaying:
   - File statistics and metadata
   - Sample translations (original vs translated)
   - Content structure overview
3. **User Flow**: Preview → Review → Proceed with full translation

## API Endpoints

### POST /translator_bot/api/preview

**Request Body:**
```json
{
    "target_language": "es",
    "file_type": ".xlsx",
    "selected_columns": ["Name", "Description"],
    "file_context": "Employee data for HR department",
    "max_items": 10
}
```

**Response:**
```json
{
    "success": true,
    "message": "Preview generated successfully",
    "data": {
        "file_info": {
            "total_rows": 100,
            "total_columns": 5,
            "selected_columns": ["Name", "Description"]
        },
        "samples": [
            {
                "column": "Name",
                "content": {"1": "John Doe", "2": "Jane Smith"}
            }
        ],
        "sample_translations": [
            {
                "original": {"column": "Name", "content": {"1": "John Doe"}},
                "translated": {"1": "Juan Pérez"},
                "column": "Name"
            }
        ],
        "statistics": {
            "Name": {
                "total_items": 100,
                "non_empty_items": 95,
                "empty_items": 5
            }
        },
        "overall_statistics": {
            "estimated_batches": 5,
            "columns_to_translate": 2,
            "total_cells_to_translate": 190
        }
    },
    "file_type": "excel",
    "target_language": "es",
    "columns_selected": ["Name", "Description"]
}
```

## File Type Specific Features

### Excel Files (.xlsx)
- Shows sample data from selected columns
- Displays statistics (total rows, empty cells, etc.)
- Estimates number of translation batches
- Respects column selection from UI

### Word Documents (.docx)
- Shows sample paragraphs
- Includes table content if present
- Displays document statistics (paragraph count, etc.)
- Handles PDF-converted documents

### PowerPoint Presentations (.pptx)
- Shows sample text from slides
- Includes speaker notes if present
- Displays slide count and structure
- Shows different content types (text shapes, notes)

### PDF Files (.pdf)
- Converts to Word format first
- Shows preview of converted content
- Indicates conversion status in preview

## Usage Examples

### Basic Preview
```javascript
// Frontend JavaScript
function showPreview() {
    const previewData = {
        target_language: 'es',
        file_type: '.xlsx',
        selected_columns: ['Name', 'Description'],
        max_items: 10
    };
    
    fetch('/translator_bot/api/preview', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(previewData)
    })
    .then(response => response.json())
    .then(data => displayPreviewModal(data.data));
}
```

### Backend Service Usage
```python
# Python backend
from src.backend.blueprints.translator_bot.translation_service import TranslationService

service = TranslationService(user_id="123", original_filename="data.xlsx")
preview_result = service.get_preview(
    target_language="es",
    selected_columns=["Name", "Description"],
    max_items=5
)
```

## Configuration

### Preview Limits
- **max_items**: Maximum number of content samples (default: 10)
- **sample_translations**: Limited to 3 samples to avoid excessive API calls
- **content_preview**: Shows first 100 characters of long text

### Performance Considerations
- Preview uses minimal API calls (max 3 translation samples)
- File reading is optimized for preview (limited data loading)
- Caching could be added for repeated preview requests

## Error Handling

The preview feature includes comprehensive error handling:

1. **File Not Found**: Returns appropriate error message
2. **Unsupported File Type**: Clear error indication
3. **Translation API Errors**: Graceful fallback with error details
4. **Network Issues**: Frontend retry mechanisms

## Testing

Run the preview feature tests:
```bash
python -m pytest tests/test_translator_preview.py -v
```

Test coverage includes:
- File handler preview methods
- Translation service integration
- API endpoint functionality
- Error scenarios
- Different file types

## Future Enhancements

Potential improvements:
1. **Caching**: Cache preview results for repeated requests
2. **More Samples**: Allow users to request more translation samples
3. **Interactive Preview**: Allow editing of context based on preview results
4. **Batch Preview**: Preview multiple target languages simultaneously
5. **Export Preview**: Allow downloading preview results
